2025-09-18 23:27:45,690 - INFO - SPY.py:2108 - Logging configured for Option Trader v1.
2025-09-18 23:27:45,690 - INFO - SPY.py:2109 - Log file: C:\Users\<USER>\Desktop\Spy option\rl_logs_yfinance_options_v1\spy_option_training_v1.log
2025-09-18 23:27:45,690 - INFO - SPY.py:2110 - Tickers to fetch: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-18 23:27:45,690 - INFO - SPY.py:2118 - YFinance session management disabled - letting yfinance handle its own sessions with curl_cffi.
2025-09-18 23:27:45,690 - INFO - SPY.py:5465 - --- Option Trader v3.1 ---
2025-09-18 23:27:45,690 - INFO - SPY.py:5475 - Signal generation mode...
2025-09-18 23:27:45,690 - WARNING - SPY.py:5514 - SIGNAL MODE: Could not find best hyperparameters file. Using default parameters for signal generation.
2025-09-18 23:27:47,227 - INFO - SPY.py:2257 - Using min_periods_override=50 for SPY
2025-09-18 23:27:47,227 - INFO - SPY.py:2405 - Skipping additional feature calculations for SPY - using reduced feature set
2025-09-18 23:27:47,227 - INFO - SPY.py:2408 - SPY: Keeping columns ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY']
2025-09-18 23:27:47,227 - INFO - SPY.py:2436 - Processed SPY data shape: (63, 5). Columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY']
2025-09-18 23:27:47,227 - INFO - SPY.py:2437 - Data quality check: 0/315 (0.0%) zero values
2025-09-18 23:27:47,227 - INFO - SPY.py:713 - [INFO] PerformanceMonitor: Operation fetch_refactored_SPY_1758209265 SUCCESS in 1.54s [Ticker: SPY] [Operation: refactored_fetch]
2025-09-18 23:27:48,037 - INFO - SPY.py:1936 - Attempt 1/3 with timeout 20s
2025-09-18 23:27:48,444 - INFO - SPY.py:2257 - Using min_periods_override=50 for ^VIX
2025-09-18 23:27:48,444 - INFO - SPY.py:2281 - Processing ^VIX data with shape (63, 7)
2025-09-18 23:27:48,444 - INFO - SPY.py:2282 - Raw ^VIX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-18 23:27:48,444 - INFO - SPY.py:2284 - Raw ^VIX Close values - first: 20.510000228881836, last: 20.81999969482422
2025-09-18 23:27:48,444 - INFO - SPY.py:2204 - Applying VIX validation for VIX
2025-09-18 23:27:48,459 - INFO - SPY.py:2363 - Processed VIX close values - first: 20.510000228881836, last: 20.81999969482422
2025-09-18 23:27:48,459 - INFO - SPY.py:2396 - Market index ^VIX: Keeping only ['close_VIX']
2025-09-18 23:27:48,459 - INFO - SPY.py:2436 - Processed ^VIX data shape: (63, 1). Columns: ['close_VIX']
2025-09-18 23:27:48,459 - INFO - SPY.py:2437 - Data quality check: 0/63 (0.0%) zero values
2025-09-18 23:27:48,459 - INFO - SPY.py:983 - VALIDATION: ^VIX last value: 20.81999969482422
2025-09-18 23:27:48,459 - INFO - SPY.py:713 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX_1758209267 SUCCESS in 1.23s [Ticker: ^VIX] [Operation: refactored_fetch]
2025-09-18 23:27:49,263 - INFO - SPY.py:1936 - Attempt 1/3 with timeout 25s
2025-09-18 23:27:49,654 - INFO - SPY.py:2257 - Using min_periods_override=50 for ^VIX3M
2025-09-18 23:27:49,654 - INFO - SPY.py:2396 - Market index ^VIX3M: Keeping only ['close_VIX3M']
2025-09-18 23:27:49,654 - INFO - SPY.py:2436 - Processed ^VIX3M data shape: (63, 1). Columns: ['close_VIX3M']
2025-09-18 23:27:49,654 - INFO - SPY.py:2437 - Data quality check: 0/63 (0.0%) zero values
2025-09-18 23:27:49,654 - INFO - SPY.py:983 - VALIDATION: ^VIX3M last value: 22.6200008392334
2025-09-18 23:27:49,654 - INFO - SPY.py:713 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX3M_1758209268 SUCCESS in 1.19s [Ticker: ^VIX3M] [Operation: refactored_fetch]
2025-09-18 23:27:50,465 - INFO - SPY.py:1936 - Attempt 1/3 with timeout 20s
2025-09-18 23:27:50,965 - INFO - SPY.py:2257 - Using min_periods_override=50 for ^IRX
2025-09-18 23:27:50,965 - INFO - SPY.py:2281 - Processing ^IRX data with shape (63, 7)
2025-09-18 23:27:50,965 - INFO - SPY.py:2282 - Raw ^IRX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-18 23:27:50,965 - INFO - SPY.py:2284 - Raw ^IRX Close values - first: 4.188000202178955, last: 4.239999771118164
2025-09-18 23:27:50,965 - INFO - SPY.py:2221 - Applying standardized Treasury rate validation for IRX (3-month Treasury yield)
2025-09-18 23:27:50,965 - INFO - SPY.py:2222 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-18 23:27:50,965 - INFO - SPY.py:2231 - IRX (3-month Treasury yield) converted from percentage to decimal format
2025-09-18 23:27:50,965 - INFO - SPY.py:2234 - IRX (3-month Treasury yield) after conversion to decimal: min=0.041500, max=0.042850, median=0.042080
2025-09-18 23:27:50,965 - INFO - SPY.py:2363 - Processed IRX (3-month Treasury yield) close values - first: 0.04188000202178955, last: 0.04239999771118164
2025-09-18 23:27:50,965 - INFO - SPY.py:2370 - VALIDATION: Final IRX (3-month Treasury yield) rate (decimal format): 0.042400 (4.2400%)
2025-09-18 23:27:50,965 - INFO - SPY.py:2396 - Market index ^IRX: Keeping only ['close_IRX']
2025-09-18 23:27:50,965 - INFO - SPY.py:2436 - Processed ^IRX data shape: (63, 1). Columns: ['close_IRX']
2025-09-18 23:27:50,965 - INFO - SPY.py:2437 - Data quality check: 0/63 (0.0%) zero values
2025-09-18 23:27:50,965 - INFO - SPY.py:983 - VALIDATION: ^IRX last value: 0.04239999771118164
2025-09-18 23:27:50,981 - INFO - SPY.py:713 - [INFO] PerformanceMonitor: Operation fetch_refactored_^IRX_1758209269 SUCCESS in 1.33s [Ticker: ^IRX] [Operation: refactored_fetch]
2025-09-18 23:27:51,782 - INFO - SPY.py:1936 - Attempt 1/3 with timeout 20s
2025-09-18 23:27:52,079 - INFO - SPY.py:2257 - Using min_periods_override=50 for ^TNX
2025-09-18 23:27:52,079 - INFO - SPY.py:2221 - Applying standardized Treasury rate validation for TNX (10-year Treasury yield)
2025-09-18 23:27:52,079 - INFO - SPY.py:2222 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-18 23:27:52,079 - INFO - SPY.py:2231 - TNX (10-year Treasury yield) converted from percentage to decimal format
2025-09-18 23:27:52,079 - INFO - SPY.py:2234 - TNX (10-year Treasury yield) after conversion to decimal: min=0.039850, max=0.045960, median=0.043650
2025-09-18 23:27:52,079 - INFO - SPY.py:2363 - Processed TNX (10-year Treasury yield) close values - first: 0.04306000232696533, last: 0.04423999786376953
2025-09-18 23:27:52,079 - INFO - SPY.py:2370 - VALIDATION: Final TNX (10-year Treasury yield) rate (decimal format): 0.044240 (4.4240%)
2025-09-18 23:27:52,079 - INFO - SPY.py:2396 - Market index ^TNX: Keeping only ['close_TNX']
2025-09-18 23:27:52,079 - INFO - SPY.py:2436 - Processed ^TNX data shape: (63, 1). Columns: ['close_TNX']
2025-09-18 23:27:52,079 - INFO - SPY.py:2437 - Data quality check: 0/63 (0.0%) zero values
2025-09-18 23:27:52,079 - INFO - SPY.py:983 - VALIDATION: ^TNX last value: 0.04423999786376953
2025-09-18 23:27:52,079 - INFO - SPY.py:713 - [INFO] PerformanceMonitor: Operation fetch_refactored_^TNX_1758209270 SUCCESS in 1.10s [Ticker: ^TNX] [Operation: refactored_fetch]
2025-09-18 23:27:52,079 - INFO - SPY.py:2600 - Created master index with 63 unique dates from 2025-03-17 to 2025-06-13
2025-09-18 23:27:52,079 - INFO - SPY.py:2605 - Processing ticker SPY for reindexing: shape=(63, 5), columns=['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY']
2025-09-18 23:27:52,079 - INFO - SPY.py:2611 - About to reindex SPY with master_index length 63
2025-09-18 23:27:52,079 - INFO - SPY.py:2497 - Index overlap for SPY: 100.0% (63/63 dates)
2025-09-18 23:27:52,095 - INFO - SPY.py:2560 - Successfully reindexed SPY with validation passed
2025-09-18 23:27:52,095 - INFO - SPY.py:2615 - Successfully reindexed SPY to master index. New shape: (63, 5)
2025-09-18 23:27:52,095 - INFO - SPY.py:2605 - Processing ticker ^VIX for reindexing: shape=(63, 1), columns=['close_VIX']
2025-09-18 23:27:52,095 - INFO - SPY.py:2611 - About to reindex ^VIX with master_index length 63
2025-09-18 23:27:52,095 - INFO - SPY.py:2497 - Index overlap for ^VIX: 100.0% (63/63 dates)
2025-09-18 23:27:52,095 - INFO - SPY.py:2560 - Successfully reindexed ^VIX with validation passed
2025-09-18 23:27:52,095 - INFO - SPY.py:2615 - Successfully reindexed ^VIX to master index. New shape: (63, 1)
2025-09-18 23:27:52,095 - INFO - SPY.py:2605 - Processing ticker ^VIX3M for reindexing: shape=(63, 1), columns=['close_VIX3M']
2025-09-18 23:27:52,095 - INFO - SPY.py:2611 - About to reindex ^VIX3M with master_index length 63
2025-09-18 23:27:52,095 - INFO - SPY.py:2497 - Index overlap for ^VIX3M: 100.0% (63/63 dates)
2025-09-18 23:27:52,095 - INFO - SPY.py:2560 - Successfully reindexed ^VIX3M with validation passed
2025-09-18 23:27:52,095 - INFO - SPY.py:2615 - Successfully reindexed ^VIX3M to master index. New shape: (63, 1)
2025-09-18 23:27:52,095 - INFO - SPY.py:2605 - Processing ticker ^IRX for reindexing: shape=(63, 1), columns=['close_IRX']
2025-09-18 23:27:52,095 - INFO - SPY.py:2611 - About to reindex ^IRX with master_index length 63
2025-09-18 23:27:52,095 - INFO - SPY.py:2497 - Index overlap for ^IRX: 100.0% (63/63 dates)
2025-09-18 23:27:52,095 - INFO - SPY.py:2560 - Successfully reindexed ^IRX with validation passed
2025-09-18 23:27:52,095 - INFO - SPY.py:2615 - Successfully reindexed ^IRX to master index. New shape: (63, 1)
2025-09-18 23:27:52,095 - INFO - SPY.py:2605 - Processing ticker ^TNX for reindexing: shape=(63, 1), columns=['close_TNX']
2025-09-18 23:27:52,095 - INFO - SPY.py:2611 - About to reindex ^TNX with master_index length 63
2025-09-18 23:27:52,095 - INFO - SPY.py:2497 - Index overlap for ^TNX: 100.0% (63/63 dates)
2025-09-18 23:27:52,095 - INFO - SPY.py:2560 - Successfully reindexed ^TNX with validation passed
2025-09-18 23:27:52,095 - INFO - SPY.py:2615 - Successfully reindexed ^TNX to master index. New shape: (63, 1)
2025-09-18 23:27:52,095 - INFO - SPY.py:2633 - Starting combine features with SPY shape: (63, 5)
2025-09-18 23:27:52,095 - INFO - SPY.py:2634 - Available tickers in reindexed_data_dict: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-18 23:27:52,095 - INFO - SPY.py:2649 - Merging ^VIX (Shape: (63, 1), Columns: ['close_VIX'])
2025-09-18 23:27:52,095 - INFO - SPY.py:2659 - Columns for ^VIX already appear to be renamed. Using as-is.
2025-09-18 23:27:52,095 - INFO - SPY.py:2695 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-18 23:27:52,095 - INFO - SPY.py:2696 - ^VIX index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-18 23:27:52,095 - INFO - SPY.py:2713 - ^VIX close_VIX sample after join (first 3): [20.510000228881836, 21.700000762939453, 19.899999618530273], last: 20.81999969482422
2025-09-18 23:27:52,095 - INFO - SPY.py:2731 - After joining ^VIX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX']
2025-09-18 23:27:52,095 - INFO - SPY.py:2649 - Merging ^VIX3M (Shape: (63, 1), Columns: ['close_VIX3M'])
2025-09-18 23:27:52,095 - INFO - SPY.py:2659 - Columns for ^VIX3M already appear to be renamed. Using as-is.
2025-09-18 23:27:52,095 - INFO - SPY.py:2695 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-18 23:27:52,095 - INFO - SPY.py:2696 - ^VIX3M index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-18 23:27:52,095 - INFO - SPY.py:2713 - ^VIX3M close_VIX3M sample after join (first 3): [21.299999237060547, 21.969999313354492, 20.829999923706055], last: 22.6200008392334
2025-09-18 23:27:52,095 - INFO - SPY.py:2731 - After joining ^VIX3M, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX', 'close_VIX3M']
2025-09-18 23:27:52,095 - INFO - SPY.py:2649 - Merging ^IRX (Shape: (63, 1), Columns: ['close_IRX'])
2025-09-18 23:27:52,095 - INFO - SPY.py:2659 - Columns for ^IRX already appear to be renamed. Using as-is.
2025-09-18 23:27:52,095 - INFO - SPY.py:2695 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-18 23:27:52,095 - INFO - SPY.py:2696 - ^IRX index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-18 23:27:52,095 - INFO - SPY.py:2713 - ^IRX close_IRX sample after join (first 3): [0.04188000202178955, 0.04195000171661377, 0.04190000057220459], last: 0.04239999771118164
2025-09-18 23:27:52,110 - INFO - SPY.py:2731 - After joining ^IRX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX']
2025-09-18 23:27:52,110 - INFO - SPY.py:2649 - Merging ^TNX (Shape: (63, 1), Columns: ['close_TNX'])
2025-09-18 23:27:52,110 - INFO - SPY.py:2659 - Columns for ^TNX already appear to be renamed. Using as-is.
2025-09-18 23:27:52,110 - INFO - SPY.py:2695 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-18 23:27:52,110 - INFO - SPY.py:2696 - ^TNX index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-18 23:27:52,110 - INFO - SPY.py:2713 - ^TNX close_TNX sample after join (first 3): [0.04306000232696533, 0.042810001373291016, 0.042560000419616696], last: 0.04423999786376953
2025-09-18 23:27:52,110 - INFO - SPY.py:2731 - After joining ^TNX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX', 'close_TNX']
2025-09-18 23:27:52,110 - INFO - SPY.py:2742 - Shape after merging all tickers: (63, 9)
2025-09-18 23:27:52,110 - INFO - SPY.py:2897 - Skipping VIX derived feature calculations - using reduced feature set
2025-09-18 23:27:52,110 - INFO - SPY.py:2901 - Skipping Treasury derived feature calculations - using reduced feature set
2025-09-18 23:27:52,110 - INFO - SPY.py:2796 - Skipping derived feature validation - using reduced feature set
2025-09-18 23:27:52,110 - INFO - SPY.py:2801 - Applying normalization to volume_SPY feature...
2025-09-18 23:27:52,110 - INFO - SPY.py:2818 - Volume normalization applied:
2025-09-18 23:27:52,110 - INFO - SPY.py:2819 -   Original volume range: 37603400 to 256611400
2025-09-18 23:27:52,110 - INFO - SPY.py:2820 -   Log-transformed range: 17.4426 to 19.3631
2025-09-18 23:27:52,110 - INFO - SPY.py:2821 -   Normalized range: -1.6874 to 3.2291
2025-09-18 23:27:52,110 - INFO - SPY.py:2822 -   Normalized mean: 0.0000, std: 1.0000
2025-09-18 23:27:52,110 - INFO - SPY.py:2829 - Performing final validation and cleanup...
2025-09-18 23:27:52,110 - INFO - SPY.py:2848 - Excluding close_VIX3M from expected columns validation (will be removed from final feature set)
2025-09-18 23:27:52,110 - INFO - SPY.py:2950 - Feature distribution verification completed successfully
2025-09-18 23:27:52,110 - INFO - SPY.py:2875 - Removing close_VIX3M from final feature set to reduce from 9 to 8 features
2025-09-18 23:27:52,110 - INFO - SPY.py:2877 - Successfully removed close_VIX3M. VIX3M data collection logic remains intact for other uses.
2025-09-18 23:27:52,110 - INFO - SPY.py:2881 - Final combined features shape: (63, 8)
2025-09-18 23:27:52,110 - INFO - SPY.py:2882 - Final columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX', 'close_IRX', 'close_TNX']
2025-09-18 23:27:52,110 - INFO - SPY.py:5527 - N_MARKET_FEATURES: 8
2025-09-18 23:27:52,110 - INFO - SPY.py:5532 - === DETERMINISTIC MODEL SELECTION FOR SIGNAL GENERATION ===
2025-09-18 23:27:52,110 - INFO - SPY.py:6952 - === MODEL SELECTION SUMMARY ===
2025-09-18 23:27:52,110 - INFO - SPY.py:6995 -   1. latest_model_with_stats.zip: [INVALID] | Size: 7079335 bytes | Modified: 2025-09-14 01:48:05.129607
2025-09-18 23:27:52,110 - INFO - SPY.py:6995 -   2. ppo_spy_option_trader_v1.zip: [VALID] | Size: 6799425 bytes | Modified: 2025-09-15 02:15:22.530008 | Stats: [OK]
2025-09-18 23:27:52,110 - INFO - SPY.py:6995 -   3. best_model.zip: [VALID] | Size: 6799421 bytes | Modified: 2025-09-15 02:14:26.823448 | Stats: [OK]
2025-09-18 23:27:52,110 - INFO - SPY.py:6997 - === END MODEL SELECTION SUMMARY ===
2025-09-18 23:27:52,110 - WARNING - SPY.py:7036 - MODEL_SELECTION: [INVALID] latest_model_with_stats.zip: Combined model (2025-09-14 01:48:05.129607) is older than final model (2025-09-15 02:15:22.530008)
2025-09-18 23:27:52,110 - INFO - SPY.py:7045 - MODEL_SELECTION: [VALIDATED] final model ppo_spy_option_trader_v1.zip (modified: 2025-09-15 02:15:22.530008)
2025-09-18 23:27:52,110 - INFO - SPY.py:7046 - MODEL_SELECTION: Validation details: File size: 6799425 bytes, Modified: 2025-09-15 02:15:22.530008, Age: 93.2 hours
2025-09-18 23:27:52,110 - INFO - SPY.py:5539 - SIGNAL_GENERATION: Selected model: C:\Users\<USER>\Desktop\Spy option\models_options_v1\ppo_spy_option_trader_v1.zip
2025-09-18 23:27:52,110 - INFO - SPY.py:5540 - SIGNAL_GENERATION: Selection reason: final_trained_model
2025-09-18 23:27:52,110 - INFO - SPY.py:5542 - SIGNAL_GENERATION: Using stats: C:\Users\<USER>\Desktop\Spy option\rl_logs_yfinance_options_v1\vec_normalize_options_yfinance_v1.pkl
2025-09-18 23:27:52,110 - INFO - SPY.py:7349 - --- Trading Signal Generation v1 ---
2025-09-18 23:27:52,110 - INFO - SPY.py:7360 - === DETERMINISTIC MODEL SELECTION FOR SIGNAL GENERATION ===
2025-09-18 23:27:52,110 - INFO - SPY.py:6952 - === MODEL SELECTION SUMMARY ===
2025-09-18 23:27:52,110 - INFO - SPY.py:6995 -   1. latest_model_with_stats.zip: [INVALID] | Size: 7079335 bytes | Modified: 2025-09-14 01:48:05.129607
2025-09-18 23:27:52,110 - INFO - SPY.py:6995 -   2. ppo_spy_option_trader_v1.zip: [VALID] | Size: 6799425 bytes | Modified: 2025-09-15 02:15:22.530008 | Stats: [OK]
2025-09-18 23:27:52,110 - INFO - SPY.py:6995 -   3. best_model.zip: [VALID] | Size: 6799421 bytes | Modified: 2025-09-15 02:14:26.823448 | Stats: [OK]
2025-09-18 23:27:52,110 - INFO - SPY.py:6997 - === END MODEL SELECTION SUMMARY ===
2025-09-18 23:27:52,110 - WARNING - SPY.py:7036 - MODEL_SELECTION: [INVALID] latest_model_with_stats.zip: Combined model (2025-09-14 01:48:05.129607) is older than final model (2025-09-15 02:15:22.530008)
2025-09-18 23:27:52,110 - INFO - SPY.py:7045 - MODEL_SELECTION: [VALIDATED] final model ppo_spy_option_trader_v1.zip (modified: 2025-09-15 02:15:22.530008)
2025-09-18 23:27:52,110 - INFO - SPY.py:7046 - MODEL_SELECTION: Validation details: File size: 6799425 bytes, Modified: 2025-09-15 02:15:22.530008, Age: 93.2 hours
2025-09-18 23:27:52,110 - INFO - SPY.py:7368 - SIGNAL_GENERATION: Selected model: C:\Users\<USER>\Desktop\Spy option\models_options_v1\ppo_spy_option_trader_v1.zip
2025-09-18 23:27:52,110 - INFO - SPY.py:7369 - SIGNAL_GENERATION: Selection reason: final_trained_model
2025-09-18 23:27:52,110 - INFO - SPY.py:7392 - SIGNAL_GENERATION: Loading model from C:\Users\<USER>\Desktop\Spy option\models_options_v1\ppo_spy_option_trader_v1.zip
2025-09-18 23:27:53,751 - INFO - SPY.py:7396 - SIGNAL_GENERATION: Will load normalization stats from C:\Users\<USER>\Desktop\Spy option\rl_logs_yfinance_options_v1\vec_normalize_options_yfinance_v1.pkl
2025-09-18 23:27:53,751 - INFO - SPY.py:7404 - Model observation space shape: 201
2025-09-18 23:27:53,751 - INFO - SPY.py:7418 - CRITICAL FIX: Model expects 201 total features with 8 market features
2025-09-18 23:27:53,751 - INFO - SPY.py:7419 - Current environment has 8 market features, need to pad 0 features
2025-09-18 23:27:53,751 - INFO - SPY.py:7420 - Static features: 5 portfolio + 20 option = 25 total
2025-09-18 23:27:53,751 - INFO - SPY.py:7430 - Creating dummy environment with observation shape: 201
2025-09-18 23:27:53,751 - INFO - SPY.py:7445 - SIGNAL_GENERATION: Loading normalization stats from C:\Users\<USER>\Desktop\Spy option\rl_logs_yfinance_options_v1\vec_normalize_options_yfinance_v1.pkl
2025-09-18 23:27:53,751 - ERROR - SPY.py:7831 - Error generating signal: spaces must have the same shape: (223,) != (201,)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Spy option\SPY.py", line 7446, in generate_trading_signal
    vec_env_predict=VecNormalize.load(selected_stats_path, vec_env_predict)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\stable_baselines3\common\vec_env\vec_normalize.py", line 321, in load
    vec_normalize.set_venv(venv)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\stable_baselines3\common\vec_env\vec_normalize.py", line 171, in set_venv
    utils.check_shape_equal(self.observation_space, venv.observation_space)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\stable_baselines3\common\utils.py", line 254, in check_shape_equal
    assert space1.shape == space2.shape, f"spaces must have the same shape: {space1.shape} != {space2.shape}"
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: spaces must have the same shape: (223,) != (201,)
2025-09-18 23:27:53,751 - INFO - SPY.py:5561 - Signal saved to trading_signal_v1.json
2025-09-18 23:27:53,751 - INFO - SPY.py:8960 - [INFO] Main: Script execution completed
2025-09-18 23:27:53,751 - INFO - SPY.py:758 - [INFO] PerformanceMonitor: Performance Summary: 5/5 operations successful (100.0% success rate)
2025-09-18 23:27:53,751 - INFO - SPY.py:766 - [INFO] PerformanceMonitor: Average refactored_fetch time: 1.28s
